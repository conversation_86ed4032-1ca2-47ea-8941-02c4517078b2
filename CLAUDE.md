# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述
这是一个简单的双人对战坦克游戏，使用HTML5 Canvas和原生JavaScript实现。玩家可以控制两辆坦克进行对战，先获得5分的玩家获胜。

## 项目结构
- `index.html` - 主页面，包含游戏画布和控制按钮
- `game.js` - 游戏核心逻辑，包含所有游戏类和主要功能
- `style.css` - 游戏样式和UI设计

## 核心架构
游戏采用面向对象设计，包含以下主要类：

### 游戏类
- **TankGame**: 主游戏控制器，管理游戏状态、渲染和更新循环
- **Tank**: 坦克类，处理移动、射击和渲染
- **Bullet**: 子弹类，处理子弹移动和碰撞检测
- **Obstacle**: 障碍物类，提供静态碰撞物体

### 游戏流程
1. 游戏初始化时创建坦克、障碍物
2. 点击"开始游戏"按钮启动游戏循环
3. 玩家1使用WASD移动，空格射击
4. 玩家2使用方向键移动，回车射击
5. 击中对方坦克得1分，先获得5分获胜

## 开发命令
- **启动游戏**: 直接在浏览器中打开 `index.html`
- **调试**: 使用浏览器开发者工具查看控制台日志

## 技术特点
- 使用 `requestAnimationFrame` 实现游戏循环
- 基于Canvas的2D渲染
- 简单的碰撞检测系统
- 键盘输入处理
- 游戏状态管理（运行中/暂停/结束）