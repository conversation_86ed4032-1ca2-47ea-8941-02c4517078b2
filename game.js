// 全局游戏实例
let game;

class TankGame {
    constructor() {
        this.canvas = document.getElementById('gameCanvas');
        this.ctx = this.canvas.getContext('2d');
        this.gameRunning = false;
        this.score1 = 0;
        this.score2 = 0;
        
        // 游戏对象
        this.tanks = [];
        this.bullets = [];
        this.obstacles = [];
        
        // 键盘状态
        this.keys = {};
        
        // 游戏循环状态
        this.loopStarted = false;
        
        // 初始化游戏
        this.init();
    }
    
    init() {
        console.log("游戏初始化");
        this.setupEventListeners();
        this.createObstacles();
        this.createTanks();
        this.render(); // 初始渲染一次，但不启动游戏循环
    }
    
    setupEventListeners() {
        console.log("设置事件监听器");
        // 键盘事件
        document.addEventListener('keydown', (e) => {
            this.keys[e.code] = true;
        });
        
        document.addEventListener('keyup', (e) => {
            this.keys[e.code] = false;
        });
        
        // 按钮事件
        const startBtn = document.getElementById('startBtn');
        const resetBtn = document.getElementById('resetBtn');
        
        if (startBtn) {
            console.log("找到开始按钮");
            startBtn.addEventListener('click', () => {
                console.log("点击开始按钮");
                this.startGame();
            });
        } else {
            console.error("未找到开始按钮");
        }
        
        if (resetBtn) {
            resetBtn.addEventListener('click', () => {
                console.log("点击重置按钮");
                this.resetGame();
            });
        } else {
            console.error("未找到重置按钮");
        }
    }
    
    createObstacles() {
        this.obstacles = [];
        
        // 创建一些障碍物
        const obstaclePositions = [
            {x: 200, y: 150, width: 100, height: 30},
            {x: 500, y: 150, width: 100, height: 30},
            {x: 350, y: 300, width: 100, height: 30},
            {x: 150, y: 450, width: 100, height: 30},
            {x: 550, y: 450, width: 100, height: 30},
            {x: 300, y: 100, width: 30, height: 100},
            {x: 470, y: 400, width: 30, height: 100}
        ];
        
        obstaclePositions.forEach(pos => {
            this.obstacles.push(new Obstacle(pos.x, pos.y, pos.width, pos.height));
        });
    }
    
    createTanks() {
        this.tanks = [];
        
        // 玩家1坦克
        this.tanks.push(new Tank(100, 300, '#e74c3c', 'player1'));
        
        // 玩家2坦克
        this.tanks.push(new Tank(700, 300, '#3498db', 'player2'));
    }
    
    startGame() {
        console.log("开始游戏");
        // 重置游戏状态
        this.score1 = 0;
        this.score2 = 0;
        this.bullets = [];
        this.createTanks();
        this.createObstacles();
        this.updateScoreDisplay();
        
        // 启动游戏
        this.gameRunning = true;
        console.log("游戏运行状态:", this.gameRunning);
        
        // 如果游戏循环尚未启动，则启动它
        if (!this.loopStarted) {
            console.log("启动游戏循环");
            this.loopStarted = true;
            this.gameLoop();
        }
    }
    
    resetGame() {
        // 重置游戏状态但不启动游戏
        this.gameRunning = false;
        this.score1 = 0;
        this.score2 = 0;
        this.bullets = [];
        this.createTanks();
        this.createObstacles();
        this.updateScoreDisplay();
        this.render(); // 重新渲染一次
    }
    
    updateScoreDisplay() {
        document.getElementById('score1').textContent = this.score1;
        document.getElementById('score2').textContent = this.score2;
    }
    
    handleInput() {
        if (!this.gameRunning) return;
        
        // 玩家1控制 (WASD + 空格)
        const tank1 = this.tanks[0];
        if (this.keys['KeyW']) tank1.move(0, -1);
        if (this.keys['KeyS']) tank1.move(0, 1);
        if (this.keys['KeyA']) tank1.move(-1, 0);
        if (this.keys['KeyD']) tank1.move(1, 0);
        if (this.keys['Space']) tank1.shoot();
        
        // 玩家2控制 (方向键 + 回车)
        const tank2 = this.tanks[1];
        if (this.keys['ArrowUp']) tank2.move(0, -1);
        if (this.keys['ArrowDown']) tank2.move(0, 1);
        if (this.keys['ArrowLeft']) tank2.move(-1, 0);
        if (this.keys['ArrowRight']) tank2.move(1, 0);
        if (this.keys['Enter']) tank2.shoot();
    }
    
    update() {
        if (!this.gameRunning) return;
        
        // 更新坦克
        this.tanks.forEach(tank => tank.update());
        
        // 更新子弹
        this.bullets = this.bullets.filter(bullet => {
            bullet.update();
            
            // 检查子弹是否击中坦克
            this.tanks.forEach(tank => {
                if (bullet.owner !== tank.id && this.checkCollision(bullet, tank)) {
                    bullet.active = false;
                    this.handleTankHit(tank);
                }
            });
            
            // 检查子弹是否击中障碍物
            this.obstacles.forEach(obstacle => {
                if (this.checkCollision(bullet, obstacle)) {
                    bullet.active = false;
                }
            });
            
            // 移除超出边界的子弹
            return bullet.active && 
                   bullet.x > 0 && bullet.x < this.canvas.width &&
                   bullet.y > 0 && bullet.y < this.canvas.height;
        });
        
        // 检查坦克碰撞
        this.checkTankCollisions();
    }
    
    checkCollision(obj1, obj2) {
        return obj1.x < obj2.x + obj2.width &&
               obj1.x + obj1.width > obj2.x &&
               obj1.y < obj2.y + obj2.height &&
               obj1.y + obj1.height > obj2.y;
    }
    
    checkTankCollisions() {
        for (let i = 0; i < this.tanks.length; i++) {
            for (let j = i + 1; j < this.tanks.length; j++) {
                if (this.checkCollision(this.tanks[i], this.tanks[j])) {
                    // 简单的碰撞响应 - 推开坦克
                    const dx = this.tanks[i].x - this.tanks[j].x;
                    const dy = this.tanks[i].y - this.tanks[j].y;
                    const distance = Math.sqrt(dx * dx + dy * dy);
                    
                    if (distance > 0) {
                        const pushX = (dx / distance) * 2;
                        const pushY = (dy / distance) * 2;
                        
                        this.tanks[i].x += pushX;
                        this.tanks[i].y += pushY;
                        this.tanks[j].x -= pushX;
                        this.tanks[j].y -= pushY;
                    }
                }
            }
            
            // 检查与障碍物的碰撞
            this.obstacles.forEach(obstacle => {
                if (this.checkCollision(this.tanks[i], obstacle)) {
                    // 简单的碰撞响应
                    const tank = this.tanks[i];
                    const prevX = tank.x - tank.vx;
                    const prevY = tank.y - tank.vy;
                    
                    if (prevX + tank.width <= obstacle.x || prevX >= obstacle.x + obstacle.width) {
                        tank.x = prevX;
                    }
                    if (prevY + tank.height <= obstacle.y || prevY >= obstacle.y + obstacle.height) {
                        tank.y = prevY;
                    }
                }
            });
        }
    }
    
    handleTankHit(tank) {
        if (tank.id === 'player1') {
            this.score2++;
        } else {
            this.score1++;
        }
        
        this.updateScoreDisplay();
        
        // 重置坦克位置
        if (tank.id === 'player1') {
            tank.x = 100;
            tank.y = 300;
        } else {
            tank.x = 700;
            tank.y = 300;
        }
        
        // 检查游戏结束
        if (this.score1 >= 5 || this.score2 >= 5) {
            this.endGame();
        }
    }
    
    endGame() {
        this.gameRunning = false;
        const winner = this.score1 >= 5 ? '玩家1' : '玩家2';
        alert(`游戏结束！${winner}获胜！`);
    }
    
    render() {
        // 清空画布
        this.ctx.fillStyle = '#34495e';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
        
        // 绘制网格背景
        this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
        this.ctx.lineWidth = 1;
        for (let i = 0; i < this.canvas.width; i += 50) {
            this.ctx.beginPath();
            this.ctx.moveTo(i, 0);
            this.ctx.lineTo(i, this.canvas.height);
            this.ctx.stroke();
        }
        for (let i = 0; i < this.canvas.height; i += 50) {
            this.ctx.beginPath();
            this.ctx.moveTo(0, i);
            this.ctx.lineTo(this.canvas.width, i);
            this.ctx.stroke();
        }
        
        // 绘制障碍物
        this.obstacles.forEach(obstacle => obstacle.render(this.ctx));
        
        // 绘制坦克
        this.tanks.forEach(tank => tank.render(this.ctx));
        
        // 绘制子弹
        this.bullets.forEach(bullet => bullet.render(this.ctx));
    }
    
    gameLoop() {
        // 处理输入和更新只在游戏运行时进行
        if (this.gameRunning) {
            this.handleInput();
            this.update();
        }
        
        // 始终渲染
        this.render();
        
        // 继续游戏循环
        requestAnimationFrame(() => this.gameLoop());
        
        // 调试信息
        if (this.gameRunning) {
            console.log("游戏循环中 - 游戏运行中");
        }
    }
    
    addBullet(bullet) {
        this.bullets.push(bullet);
    }
}

class Tank {
    constructor(x, y, color, id) {
        this.x = x;
        this.y = y;
        this.width = 40;
        this.height = 40;
        this.color = color;
        this.id = id;
        this.speed = 2;
        this.direction = 0; // 角度，0表示向上
        this.vx = 0;
        this.vy = 0;
        this.lastShot = 0;
        this.shotCooldown = 500; // 射击冷却时间（毫秒）
    }
    
    move(dx, dy) {
        this.vx = dx * this.speed;
        this.vy = dy * this.speed;
        
        // 更新方向
        if (dx !== 0 || dy !== 0) {
            this.direction = Math.atan2(dy, dx) - Math.PI / 2;
        }
    }
    
    update() {
        this.x += this.vx;
        this.y += this.vy;
        
        // 边界检查
        this.x = Math.max(0, Math.min(800 - this.width, this.x));
        this.y = Math.max(0, Math.min(600 - this.height, this.y));
        
        // 减速
        this.vx *= 0.9;
        this.vy *= 0.9;
    }
    
    shoot() {
        const now = Date.now();
        if (now - this.lastShot < this.shotCooldown) return;
        
        const bulletX = this.x + this.width / 2 - 5;
        const bulletY = this.y + this.height / 2 - 5;
        const bulletSpeed = 5;
        
        const bullet = new Bullet(
            bulletX, 
            bulletY, 
            Math.sin(this.direction) * bulletSpeed,
            -Math.cos(this.direction) * bulletSpeed,
            this.id
        );
        
        // 使用全局游戏实例
        if (game) {
            game.addBullet(bullet);
        } else {
            console.error("游戏实例未定义");
        }
        this.lastShot = now;
    }
    
    render(ctx) {
        ctx.save();
        ctx.translate(this.x + this.width / 2, this.y + this.height / 2);
        ctx.rotate(this.direction);
        
        // 绘制坦克主体
        ctx.fillStyle = this.color;
        ctx.fillRect(-this.width / 2, -this.height / 2, this.width, this.height);
        
        // 绘制坦克炮管
        ctx.fillStyle = '#2c3e50';
        ctx.fillRect(-5, -this.height / 2 - 15, 10, 20);
        
        // 绘制坦克履带
        ctx.fillStyle = '#2c3e50';
        ctx.fillRect(-this.width / 2 - 5, -this.height / 2, 5, this.height);
        ctx.fillRect(this.width / 2, -this.height / 2, 5, this.height);
        
        ctx.restore();
        
        // 绘制坦克ID
        ctx.fillStyle = 'white';
        ctx.font = '12px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(this.id === 'player1' ? 'P1' : 'P2', this.x + this.width / 2, this.y + this.height / 2 + 4);
    }
}

class Bullet {
    constructor(x, y, vx, vy, owner) {
        this.x = x;
        this.y = y;
        this.width = 10;
        this.height = 10;
        this.vx = vx;
        this.vy = vy;
        this.owner = owner;
        this.active = true;
    }
    
    update() {
        this.x += this.vx;
        this.y += this.vy;
    }
    
    render(ctx) {
        ctx.fillStyle = '#f39c12';
        ctx.beginPath();
        ctx.arc(this.x + this.width / 2, this.y + this.height / 2, 5, 0, Math.PI * 2);
        ctx.fill();
    }
}

class Obstacle {
    constructor(x, y, width, height) {
        this.x = x;
        this.y = y;
        this.width = width;
        this.height = height;
    }
    
    render(ctx) {
        ctx.fillStyle = '#7f8c8d';
        ctx.fillRect(this.x, this.y, this.width, this.height);
        
        // 添加一些纹理
        ctx.strokeStyle = '#95a5a6';
        ctx.lineWidth = 2;
        ctx.strokeRect(this.x, this.y, this.width, this.height);
    }
}

// 等待DOM加载完成后初始化游戏
document.addEventListener('DOMContentLoaded', () => {
    console.log("DOM加载完成，初始化游戏");
    // 初始化游戏
    game = new TankGame();
});
