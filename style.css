* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #2c3e50, #34495e);
    color: white;
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
}

.game-container {
    text-align: center;
    padding: 20px;
}

.game-header {
    margin-bottom: 20px;
}

.game-header h1 {
    font-size: 2.5em;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.score-board {
    display: flex;
    justify-content: center;
    gap: 50px;
    font-size: 1.2em;
    font-weight: bold;
}

#gameCanvas {
    border: 3px solid #ecf0f1;
    border-radius: 10px;
    background: #34495e;
    box-shadow: 0 0 20px rgba(0,0,0,0.5);
}

.game-controls {
    margin-top: 20px;
}

.control-info {
    margin-bottom: 20px;
}

.control-info h3 {
    margin-bottom: 10px;
    color: #3498db;
}

.controls-grid {
    display: flex;
    justify-content: center;
    gap: 50px;
    margin-bottom: 20px;
}

.controls-grid div {
    background: rgba(52, 73, 94, 0.5);
    padding: 15px;
    border-radius: 10px;
    border: 1px solid #3498db;
}

button {
    padding: 12px 30px;
    margin: 0 10px;
    font-size: 16px;
    background: #3498db;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background 0.3s;
}

button:hover {
    background: #2980b9;
}

button:active {
    transform: scale(0.95);
}

.game-over {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.8);
    padding: 40px;
    border-radius: 10px;
    text-align: center;
    display: none;
}

.game-over h2 {
    color: #e74c3c;
    margin-bottom: 20px;
    font-size: 2em;
}

.game-over p {
    font-size: 1.2em;
    margin-bottom: 20px;
}
